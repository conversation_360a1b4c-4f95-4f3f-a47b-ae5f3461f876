{% extends 'base.html' %}
{% load static %}

{% block title %}{% block dashboard_title %}Dashboard{% endblock %} - <PERSON><PERSON><PERSON>{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/dashboard-mobile.css' %}">
<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
<style>
    /* Modern Dashboard Navigation Styles */
    .dashboard-nav-link {
        display: flex;
        align-items: center;
        padding: 0.875rem 1.25rem;
        color: #111827;
        text-decoration: none;
        border-radius: 0.75rem;
        margin-bottom: 0.375rem;
        transition: all 0.3s ease;
        font-weight: 500;
        font-family: 'Inter', sans-serif;
        border: 1px solid transparent;
    }

    .dashboard-nav-link:hover {
        background-color: #F3F4F6;
        color: #DC2626;
        transform: translateX(6px);
        border-color: #E5E7EB;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    }

    .dashboard-nav-link.active {
        background: linear-gradient(135deg, #DC2626 0%, #1E3A8A 100%);
        color: white;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    }

    .dashboard-nav-link i {
        width: 1.5rem;
        margin-right: 0.875rem;
        text-align: center;
        font-size: 1.125rem;
    }

    /* Mobile sidebar styles */
    .mobile-sidebar {
        transform: translateX(-100%);
        transition: transform 0.3s ease-in-out;
    }

    .mobile-sidebar-open {
        transform: translateX(0);
    }

    /* Modern Dashboard Cards */
    .dashboard-card {
        background: white;
        border-radius: 1rem;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        border: 1px solid #E5E7EB;
        overflow: hidden;
        transition: all 0.3s ease;
    }

    .dashboard-card:hover {
        box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        transform: translateY(-2px);
    }

    .stat-card {
        background: white;
        border-radius: 1rem;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        border: 1px solid #E5E7EB;
        padding: 2rem;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .stat-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, #DC2626 0%, #1E3A8A 100%);
    }

    .stat-card:hover {
        box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        transform: translateY(-4px);
    }

    .stat-value {
        font-size: 2rem;
        font-weight: 700;
        color: #1f2937;
        margin-bottom: 0.5rem;
    }

    .stat-label {
        font-size: 0.875rem;
        color: #6b7280;
        text-transform: uppercase;
        letter-spacing: 0.05em;
        font-weight: 500;
    }

    /* Harrier button styles */
    .btn-harrier-primary {
        background-color: #ed6663;
        color: white;
        padding: 0.5rem 1rem;
        border-radius: 0.5rem;
        font-weight: 500;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        transition: all 0.2s ease-in-out;
        border: none;
        cursor: pointer;
    }

    .btn-harrier-primary:hover {
        background-color: #dc2626;
        transform: translateY(-1px);
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    }

    .btn-harrier-secondary {
        background-color: white;
        color: #ed6663;
        border: 1px solid #ed6663;
        padding: 0.5rem 1rem;
        border-radius: 0.5rem;
        font-weight: 500;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        transition: all 0.2s ease-in-out;
        cursor: pointer;
    }

    .btn-harrier-secondary:hover {
        background-color: #ed6663;
        color: white;
        transform: translateY(-1px);
    }

    /* Responsive adjustments */
    @media (max-width: 1024px) {
        .dashboard-content {
            padding: 1rem;
        }

        .stat-card {
            padding: 1rem;
        }

        .stat-value {
            font-size: 1.5rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<!-- Splash Screen -->
{% include 'components/splash_screen.html' %}

<!-- Dashboard Layout -->
<div class="min-h-screen bg-accent-gray">
    <!-- Dashboard Header -->
    <div class="bg-white shadow-sm border-b border-gray-200">
        <div class="w-full px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-4">
                <!-- Page Title -->
                <div class="flex items-center">
                    <button type="button" class="lg:hidden p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-harrier-red" id="mobile-sidebar-toggle">
                        <i class="fas fa-bars text-xl"></i>
                    </button>
                    <div class="ml-4 lg:ml-0">
                        <h1 class="text-2xl font-heading font-bold text-harrier-dark">
                            {% block page_title %}Dashboard{% endblock %}
                        </h1>
                        <p class="text-sm text-gray-600 mt-1">
                            {% block page_description %}Manage your account and activities{% endblock %}
                        </p>
                    </div>
                </div>

                <!-- Top Actions -->
                <div class="flex items-center space-x-4">
                    <!-- Notifications -->
                    <div class="relative">
                        <button type="button" class="p-2 text-gray-400 hover:text-gray-500 hover:bg-gray-100 rounded-full focus:outline-none focus:ring-2 focus:ring-harrier-red">
                            <i class="fas fa-bell text-xl"></i>
                            <span class="absolute top-0 right-0 block h-2 w-2 rounded-full bg-harrier-red"></span>
                        </button>
                    </div>

                    <!-- User Menu -->
                    <div class="relative group">
                        <button type="button" class="flex items-center space-x-3 p-2 rounded-lg hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-harrier-red">
                            <div class="w-8 h-8 bg-harrier-red rounded-full flex items-center justify-center text-white font-semibold text-sm">
                                {{ user.first_name|first|default:user.username|first|upper }}
                            </div>
                            <div class="hidden md:block text-left">
                                <p class="text-sm font-medium text-harrier-dark">{{ user.first_name|default:user.username }}</p>
                                <p class="text-xs text-gray-500">{{ user.get_role_display }}</p>
                            </div>
                            <i class="fas fa-chevron-down text-xs text-gray-400"></i>
                        </button>
                        
                        <!-- Dropdown Menu -->
                        <div class="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg py-2 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50">
                            <a href="{% url 'core:profile' %}" class="block px-4 py-2 text-sm text-harrier-dark hover:bg-harrier-gray transition-colors">
                                <i class="fas fa-user-edit mr-2"></i>Edit Profile
                            </a>
                            <a href="#" class="block px-4 py-2 text-sm text-harrier-dark hover:bg-harrier-gray transition-colors">
                                <i class="fas fa-cog mr-2"></i>Settings
                            </a>
                            <div class="border-t border-gray-200 my-1"></div>
                            <a href="{% url 'core:logout' %}" class="block px-4 py-2 text-sm text-red-600 hover:bg-red-50 transition-colors">
                                <i class="fas fa-sign-out-alt mr-2"></i>Logout
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Dashboard Content -->
    <div class="w-full px-4 sm:px-6 lg:px-8 py-8">
        <div class="flex flex-col lg:flex-row gap-8">
            <!-- Sidebar Navigation -->
            <aside class="w-full lg:w-64 flex-shrink-0">
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
                    <!-- Navigation Menu -->
                    <nav class="p-4">
                        <ul class="space-y-2">
                            {% block sidebar_nav %}
                                <!-- Check user type and display appropriate navigation -->
                                {% if user.is_authenticated %}
                                    {% if user.role == 'vendor' %}
                                        {% block vendor_sidebar_nav %}
                                            <!-- Vendor Navigation Section -->
                                            <!-- Main Navigation Section -->
                                            <div class="mb-6">
                                                <h4 class="text-xs font-semibold text-gray-500 uppercase tracking-wider mb-3 px-3">Main</h4>
                                                <li>
                                                    <a href="{% url 'core:dashboard' %}" class="dashboard-nav-link {% if request.resolver_match.url_name == 'dashboard' %}active{% endif %}">
                                                        <i class="fas fa-tachometer-alt"></i>
                                                        <span>Dashboard</span>
                                                        <div class="ml-auto">
                                                            <div class="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                                                        </div>
                                                    </a>
                                                </li>
                                                <li>
                                                    <a href="{% url 'core:profile' %}" class="dashboard-nav-link {% if 'profile' in request.resolver_match.url_name %}active{% endif %}">
                                                        <i class="fas fa-building"></i>
                                                        <span>Company Profile</span>
                                                    </a>
                                                </li>
                                            </div>

                                            <!-- Inventory & Products Section -->
                                            <div class="mb-6">
                                                <h4 class="text-xs font-semibold text-gray-500 uppercase tracking-wider mb-3 px-3">Inventory & Products</h4>
                                                <li>
                                                    <a href="{% url 'core:vendor_listings' %}" class="dashboard-nav-link {% if 'listings' in request.resolver_match.url_name %}active{% endif %}">
                                                        <i class="fas fa-car"></i>
                                                        <span>My Listings</span>
                                                        {% if vendor_cars %}
                                                            <span class="ml-auto bg-harrier-red text-white text-xs rounded-full px-2 py-1">{{ vendor_cars|length }}</span>
                                                        {% endif %}
                                                    </a>
                                                </li>
                                                <li>
                                                    <a href="{% url 'core:sell_car' %}" class="dashboard-nav-link">
                                                        <i class="fas fa-plus-circle"></i>
                                                        <span>Add New Car</span>
                                                    </a>
                                                </li>
                                                <li>
                                                    <a href="{% url 'core:vendor_spare_parts' %}" class="dashboard-nav-link {% if 'spare_parts' in request.resolver_match.url_name %}active{% endif %}">
                                                        <i class="fas fa-cogs"></i>
                                                        <span>Spare Parts</span>
                                                    </a>
                                                </li>
                                            </div>

                                            <!-- Business & Sales Section -->
                                            <div class="mb-6">
                                                <h4 class="text-xs font-semibold text-gray-500 uppercase tracking-wider mb-3 px-3">Business & Sales</h4>
                                                <li>
                                                    <a href="{% url 'core:vendor_orders' %}" class="dashboard-nav-link {% if 'orders' in request.resolver_match.url_name %}active{% endif %}">
                                                        <i class="fas fa-shopping-bag"></i>
                                                        <span>Orders</span>
                                                        {% if pending_orders %}
                                                            <span class="ml-auto bg-blue-500 text-white text-xs rounded-full px-2 py-1">{{ pending_orders }}</span>
                                                        {% endif %}
                                                    </a>
                                                </li>
                                                <li>
                                                    <a href="{% url 'core:vendor_inquiries' %}" class="dashboard-nav-link {% if 'inquiries' in request.resolver_match.url_name %}active{% endif %}">
                                                        <i class="fas fa-envelope"></i>
                                                        <span>Inquiries</span>
                                                        {% if pending_inquiries %}
                                                            <span class="ml-auto bg-orange-500 text-white text-xs rounded-full px-2 py-1">{{ pending_inquiries|length }}</span>
                                                        {% endif %}
                                                    </a>
                                                </li>
                                                <li>
                                                    <a href="{% url 'core:vendor_analytics' %}" class="dashboard-nav-link {% if 'analytics' in request.resolver_match.url_name %}active{% endif %}">
                                                        <i class="fas fa-chart-bar"></i>
                                                        <span>Analytics</span>
                                                    </a>
                                                </li>
                                            </div>

                                            <!-- Account & Settings Section -->
                                            <div class="mb-6">
                                                <h4 class="text-xs font-semibold text-gray-500 uppercase tracking-wider mb-3 px-3">Account & Settings</h4>
                                                <li>
                                                    <a href="{% url 'core:vendor_settings' %}" class="dashboard-nav-link {% if 'settings' in request.resolver_match.url_name %}active{% endif %}">
                                                        <i class="fas fa-cog"></i>
                                                        <span>Settings</span>
                                                    </a>
                                                </li>
                                            </div>
                                        {% endblock %}
                                    {% else %}
                                        {% block user_sidebar_nav %}
                                            <!-- User/Customer Navigation Section -->
                                            <!-- Main Navigation Section -->
                                            <div class="mb-6">
                                                <h4 class="text-xs font-semibold text-gray-500 uppercase tracking-wider mb-3 px-3">Main</h4>
                                                <li>
                                                    <a href="{% url 'core:dashboard' %}" class="dashboard-nav-link {% if request.resolver_match.url_name == 'dashboard' %}active{% endif %}">
                                                        <i class="fas fa-tachometer-alt"></i>
                                                        <span>Dashboard</span>
                                                        <div class="ml-auto">
                                                            <div class="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                                                        </div>
                                                    </a>
                                                </li>
                                                <li>
                                                    <a href="{% url 'core:profile' %}" class="dashboard-nav-link {% if 'profile' in request.resolver_match.url_name %}active{% endif %}">
                                                        <i class="fas fa-user-circle"></i>
                                                        <span>Profile</span>
                                                    </a>
                                                </li>
                                            </div>

                                            <!-- Orders & Purchases Section -->
                                            <div class="mb-6">
                                                <h4 class="text-xs font-semibold text-gray-500 uppercase tracking-wider mb-3 px-3">Orders & Purchases</h4>
                                                <li>
                                                    <a href="{% url 'core:user_orders' %}" class="dashboard-nav-link {% if 'orders' in request.resolver_match.url_name %}active{% endif %}">
                                                        <i class="fas fa-shopping-bag"></i>
                                                        <span>My Orders</span>
                                                        {% if pending_orders_count %}
                                                            <span class="ml-auto bg-harrier-red text-white text-xs rounded-full px-2 py-1">{{ pending_orders_count }}</span>
                                                        {% endif %}
                                                    </a>
                                                </li>
                                                <li>
                                                    <a href="{% url 'core:user_import_requests' %}" class="dashboard-nav-link {% if 'import' in request.resolver_match.url_name %}active{% endif %}">
                                                        <i class="fas fa-ship"></i>
                                                        <span>Import Requests</span>
                                                        {% if pending_imports_count %}
                                                            <span class="ml-auto bg-blue-500 text-white text-xs rounded-full px-2 py-1">{{ pending_imports_count }}</span>
                                                        {% endif %}
                                                    </a>
                                                </li>
                                            </div>

                                            <!-- My Listings Section -->
                                            <div class="mb-6">
                                                <h4 class="text-xs font-semibold text-gray-500 uppercase tracking-wider mb-3 px-3">My Listings</h4>
                                                <li>
                                                    <a href="{% url 'core:user_listings' %}" class="dashboard-nav-link {% if 'listings' in request.resolver_match.url_name %}active{% endif %}">
                                                        <i class="fas fa-car"></i>
                                                        <span>My Cars</span>
                                                        {% if user_pending_cars_count %}
                                                            <span class="ml-auto bg-yellow-500 text-white text-xs rounded-full px-2 py-1">{{ user_pending_cars_count }}</span>
                                                        {% endif %}
                                                    </a>
                                                </li>
                                            </div>

                                            <!-- Communication Section -->
                                            <div class="mb-6">
                                                <h4 class="text-xs font-semibold text-gray-500 uppercase tracking-wider mb-3 px-3">Communication</h4>
                                                <li>
                                                    <a href="{% url 'core:user_inquiries' %}" class="dashboard-nav-link {% if 'inquiries' in request.resolver_match.url_name %}active{% endif %}">
                                                        <i class="fas fa-envelope"></i>
                                                        <span>Inquiries</span>
                                                        {% if unread_inquiries_count %}
                                                            <span class="ml-auto bg-orange-500 text-white text-xs rounded-full px-2 py-1">{{ unread_inquiries_count }}</span>
                                                        {% endif %}
                                                    </a>
                                                </li>
                                            </div>

                                            <!-- Personal & Settings Section -->
                                            <div class="mb-6">
                                                <h4 class="text-xs font-semibold text-gray-500 uppercase tracking-wider mb-3 px-3">Personal & Settings</h4>
                                                <li>
                                                    <a href="{% url 'core:user_addresses' %}" class="dashboard-nav-link {% if 'addresses' in request.resolver_match.url_name %}active{% endif %}">
                                                        <i class="fas fa-map-marker-alt"></i>
                                                        <span>Addresses</span>
                                                    </a>
                                                </li>
                                                <li>
                                                    <a href="{% url 'core:user_wishlist' %}" class="dashboard-nav-link {% if 'wishlist' in request.resolver_match.url_name %}active{% endif %}">
                                                        <i class="fas fa-heart"></i>
                                                        <span>Wishlist</span>
                                                        {% if wishlist_count %}
                                                            <span class="ml-auto bg-pink-500 text-white text-xs rounded-full px-2 py-1">{{ wishlist_count }}</span>
                                                        {% endif %}
                                                    </a>
                                                </li>
                                                <li>
                                                    <a href="{% url 'core:user_settings' %}" class="dashboard-nav-link {% if 'settings' in request.resolver_match.url_name %}active{% endif %}">
                                                        <i class="fas fa-cog"></i>
                                                        <span>Settings</span>
                                                    </a>
                                                </li>
                                            </div>
                                        {% endblock %}
                                    {% endif %}
                                {% else %}
                                    <!-- Default navigation for non-authenticated users -->
                                    <li>
                                        <a href="{% url 'core:dashboard' %}" class="dashboard-nav-link {% if request.resolver_match.url_name == 'dashboard' %}active{% endif %}">
                                            <i class="fas fa-tachometer-alt"></i>
                                            <span>Dashboard</span>
                                        </a>
                                    </li>
                                    <li>
                                        <a href="{% url 'core:profile' %}" class="dashboard-nav-link {% if 'profile' in request.resolver_match.url_name %}active{% endif %}">
                                            <i class="fas fa-user"></i>
                                            <span>Profile</span>
                                        </a>
                                    </li>
                                {% endif %}
                            {% endblock %}
                        </ul>
                    </nav>
                </div>

                <!-- Quick Stats Card -->
                {% block sidebar_stats %}{% endblock %}
            </aside>

            <!-- Main Content Area -->
            <main class="flex-1 min-w-0">
                <!-- Breadcrumb -->
                {% block breadcrumb %}
                    <nav class="flex mb-6" aria-label="Breadcrumb">
                        <ol class="inline-flex items-center space-x-1 md:space-x-3">
                            <li class="inline-flex items-center">
                                <a href="{% url 'core:homepage' %}" class="inline-flex items-center text-sm font-medium text-gray-700 hover:text-harrier-red">
                                    <i class="fas fa-home mr-2"></i>Home
                                </a>
                            </li>
                            <li>
                                <div class="flex items-center">
                                    <i class="fas fa-chevron-right text-gray-400 mx-2"></i>
                                    <a href="{% url 'core:dashboard' %}" class="ml-1 text-sm font-medium text-gray-700 hover:text-harrier-red md:ml-2">Dashboard</a>
                                </div>
                            </li>
                            {% block breadcrumb_items %}{% endblock %}
                        </ol>
                    </nav>
                {% endblock %}

                <!-- Page Content -->
                {% block dashboard_content %}
                    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                        <p class="text-gray-600">Dashboard content goes here.</p>
                    </div>
                {% endblock %}
            </main>
        </div>
    </div>
</div>

<!-- Mobile Sidebar Overlay -->
<div class="fixed inset-0 z-40 lg:hidden hidden" id="mobile-sidebar-overlay">
    <div class="fixed inset-0 bg-black bg-opacity-50" id="mobile-sidebar-backdrop"></div>
    <div class="fixed inset-y-0 left-0 flex flex-col w-64 bg-white shadow-xl transform -translate-x-full transition-transform duration-300 ease-in-out" id="mobile-sidebar">
        <div class="flex items-center justify-between p-4 border-b border-gray-200">
            <h2 class="text-lg font-heading font-semibold text-harrier-dark">Menu</h2>
            <button type="button" class="p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100" id="mobile-sidebar-close">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <nav class="flex-1 p-4 overflow-y-auto">
            <!-- Mobile navigation will be populated by JavaScript -->
        </nav>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- HTMX for lazy loading -->
<script src="https://unpkg.com/htmx.org@1.9.10"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Mobile sidebar functionality
    const mobileToggle = document.getElementById('mobile-sidebar-toggle');
    const mobileOverlay = document.getElementById('mobile-sidebar-overlay');
    const mobileSidebar = document.getElementById('mobile-sidebar');
    const mobileClose = document.getElementById('mobile-sidebar-close');
    const mobileBackdrop = document.getElementById('mobile-sidebar-backdrop');

    function openMobileSidebar() {
        mobileOverlay.classList.remove('hidden');
        setTimeout(() => {
            mobileSidebar.classList.add('mobile-sidebar-open');
        }, 10);
    }

    function closeMobileSidebar() {
        mobileSidebar.classList.remove('mobile-sidebar-open');
        setTimeout(() => {
            mobileOverlay.classList.add('hidden');
        }, 300);
    }

    if (mobileToggle) {
        mobileToggle.addEventListener('click', openMobileSidebar);
    }

    if (mobileClose) {
        mobileClose.addEventListener('click', closeMobileSidebar);
    }

    if (mobileBackdrop) {
        mobileBackdrop.addEventListener('click', closeMobileSidebar);
    }

    // Copy desktop navigation to mobile
    const desktopNav = document.querySelector('aside nav ul');
    const mobileNav = document.querySelector('#mobile-sidebar nav');
    if (desktopNav && mobileNav) {
        mobileNav.innerHTML = '<ul class="space-y-2">' + desktopNav.innerHTML + '</ul>';
    }

    // Auto-close mobile sidebar on window resize
    window.addEventListener('resize', function() {
        if (window.innerWidth >= 1024) {
            closeMobileSidebar();
        }
    });

    // Initialize enhanced lazy loading
    initializeLazyLoading();
});

// Enhanced Lazy Loading System
function initializeLazyLoading() {
    // Progressive loading for dashboard sections
    const sections = [
        '.lazy-stats',
        '.lazy-user-actions',
        '.lazy-recent-listings',
        '.lazy-charts',
        '.lazy-tables'
    ];

    // Stagger loading of sections
    sections.forEach((selector, index) => {
        setTimeout(() => {
            const elements = document.querySelectorAll(selector);
            elements.forEach(element => {
                if (element.classList.contains('lazy-content-container')) {
                    htmx.trigger(element, 'revealed');
                }
            });
        }, index * 300); // 300ms delay between sections
    });
}

<!-- Toast Manager - DISABLED DUE TO RECURSION ERROR -->
<!-- {% load core_extras %} -->
<!-- {% toast_script %} -->
<!-- {% render_toast_messages %} -->
</script>
{% endblock %}
